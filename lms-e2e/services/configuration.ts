import { BaseConfiguration } from '../../shared/configurations/base-configurtaion';
import * as AppSettings from '../configurations/app-settings.json';
import * as TestmailApp from '../configurations/testmail-app.json';
import * as ShareCourses from '../configurations/courses.share.json';
import * as CoursesLocal from '../configurations/courses.local.json';
import * as CoursesWorker0 from '../configurations/courses.worker.0.json';
import * as CoursesWorker1 from '../configurations/courses.worker.1.json';
import * as ShareLearningPath from '../configurations/learning-path.share.json';
import * as ShareUsers from '../configurations/users.share.json';
import * as UsersLocal from '../configurations/users.local.json';
import * as UsersWorker0 from '../configurations/users.worker.0.json';
import * as UsersWorker1 from '../configurations/users.worker.1.json';
import * as Organization from '../configurations/organization.json';
import * as eKycs from '../configurations/ekycs.json';
import * as KnowledgeContentItemsLocal from '../configurations/knowledge-content-items.local.json';
import * as KnowledgeContentItemsWorker0 from '../configurations/knowledge-content-items.worker.0.json';
import * as KnowledgeContentItemsWorker1 from '../configurations/knowledge-content-items.worker.1.json';
import * as CourseVersions from '../configurations/course-versions.share.json';
import * as Customers from '../configurations/customers.json';
import * as ImageFaceCompare from '../configurations/image-face-compare.json';

export class Configuration extends BaseConfiguration {
  appSettings: typeof AppSettings;
  usersLocal: typeof UsersLocal;
  knowledgeContentItemsLocal: typeof KnowledgeContentItemsLocal;
  testmailApp: typeof TestmailApp;
  shareUsers: typeof ShareUsers;
  coursesLocal: typeof CoursesLocal;
  shareCourses: typeof ShareCourses;
  shareLearningPath: typeof ShareLearningPath;
  organization: typeof Organization;
  courseVersions: typeof CourseVersions;
  ekycs: typeof eKycs;
  customers: typeof Customers;
  imageFaceCompare: any;

  constructor(settings: typeof AppSettings, parallelIndex: number) {
    super(settings);
    this.appSettings = settings;
    this.usersLocal = UsersLocal;
    this.knowledgeContentItemsLocal = KnowledgeContentItemsLocal;
    this.shareUsers = ShareUsers;
    this.coursesLocal = CoursesLocal;
    this.shareCourses = ShareCourses;
    this.courseVersions = CourseVersions;
    this.shareLearningPath = ShareLearningPath;
    this.testmailApp = TestmailApp;
    this.organization = Organization;
    this.ekycs = eKycs;
    this.customers = Customers;
    this.imageFaceCompare = ImageFaceCompare;

    if (this.isTestOnCI()) {
      switch (parallelIndex) {
        case 0: {
          this.usersLocal = UsersWorker0;
          this.coursesLocal = CoursesWorker0;
          this.knowledgeContentItemsLocal = KnowledgeContentItemsWorker0;
          break;
        }
        case 1: {
          this.usersLocal = UsersWorker1;
          this.coursesLocal = CoursesWorker1;
          this.knowledgeContentItemsLocal = KnowledgeContentItemsWorker1;
          break;
        }
        default: {
          break;
        }
      }
    }
  }
}
