import { expect, Locator, Page } from '@playwright/test';
import { BasePage } from '../base-page';
import { CalendarHelper } from '../../../../shared/calendar/calendar-helper';
import * as path from 'path';

export class TrainingEnrollmentsPage extends BasePage {
  readonly nameInputLocator: Locator = this.page.getByPlaceholder('ชื่อจริง/นามสกุล');
  readonly searchButtonLocator: Locator = this.page.locator('svg[data-icon="search"]');

  readonly waitingToEnrollTabLocator: Locator = this.page.getByRole('tab', { name: 'รอลงเรียน' });
  readonly enrollmentLearningTabLocator: Locator = this.page.getByRole('tab', { name: 'กำลังเรียน' });
  readonly viewDetailButtonLocator: Locator = this.page.getByRole('button', { name: 'ดูรายละเอียด' });
  readonly verifyFullNameLocator: Locator = this.page.locator(
    'div.ant-card-body >> div.ant-space-item >> h5.ant-typography',
  );
  readonly reasonRequestDocumentInputLocator: Locator = this.page.getByPlaceholder('กรอกเหตุผลการขอเอกสาร');

  readonly requestDocumentButtonLocator: Locator = this.page.getByRole('button', { name: 'ขอเอกสาร' });
  readonly checkBoxCitizenCardLocator: Locator = this.page.locator(
    'div:nth-child(4) > .ant-tree-checkbox > .ant-tree-checkbox-inner',
  );
  readonly submitRequestDocumentButtonLocator: Locator = this.page.getByRole('button', { name: 'ยืนยัน' });
  readonly verifyHeadingDocumentLocator: Locator = this.page.getByRole('heading', { name: 'ข้อมูลเอกสารเพิ่มเติม' });
  readonly verifyHeadingRemarkLocator: Locator = this.page.getByRole('heading', { name: 'หมายเหตุ' });
  readonly editRemarkButtonLocator: Locator = this.page.getByRole('button', { name: 'แก้ไข' });
  readonly remarkInputLocator: Locator = this.page.getByPlaceholder('กรอกหมายเหตุ');
  readonly submitRemarkButtonLocator: Locator = this.page.getByRole('button', { name: 'บันทึก' });
  readonly remarkDisplayTextLocator: Locator = this.page
    .locator('div.ant-card-body:has(h4:has-text("หมายเหตุ"))')
    .locator('span.ant-typography.document-text-value');
  readonly verifyHeadingCreditHistoryLocator: Locator = this.page.getByRole('heading', { name: 'การใช้งานเครดิต' });
  readonly searchFullnNameLocator: Locator = this.page.locator('#enrollmentFullName');
  readonly searchResultLocator: Locator = this.page.getByRole('heading', { name: 'ผลการค้นหา:' });
  readonly columnFullNameLocator: Locator = this.page.locator(
    'td.ant-table-cell:below(th.ant-table-cell:text("คำนำหน้า/ชื่อจริง/ชื่อกลาง/นามสกุล")) >> nth=0',
  );
  readonly columnCourseNameLocator: Locator = this.page.locator(
    'td.ant-table-cell:below(th.ant-table-cell:text("หลักสูตร")) >> nth=0',
  );
  readonly searchEmailLocator: Locator = this.page.locator('div.ant-space-item >> span.prefix:has-text("อีเมล")');
  readonly inputEmailLocator: Locator = this.page.locator('input#inputText');
  readonly inputExpiredAtLocator: Locator = this.page.locator('#expiredAt');
  readonly tabSelectDateLocator: Locator = this.page.locator('.ant-picker-input');
  readonly nextMonthButtonLocator: Locator = this.page.locator('button[aria-label="next-year"]');
  readonly calendarHeaderLocator: Locator = this.page.locator('button[aria-label="month panel"]');
  private readonly selectDateOnCalendarLocator: string = 'css=td[title="${date}"] >> nth=0';
  readonly enrollmentStatusOnFooterLocator: Locator = this.page
    .locator('footer.ant-layout-footer >> div.ant-space-item:right-of(span:has-text("สถานะ:"))')
    .first();

  // Check state
  readonly loadingSkeleton: Locator = this.page
    .locator('td > .ant-skeleton > .ant-skeleton-content > .ant-skeleton-title')
    .first();
  readonly toastCreateAdditionDocumentSuccessLocator: Locator = this.page.getByText(
    'สร้างรายการขอเอกสารเพิ่มเติมสำเร็จ',
  );
  readonly toastSuccessLocator: Locator = this.page.getByText('บันทึกข้อมูลสำเร็จ');
  readonly toastDeleteImageSuccessLocator: Locator = this.page.getByText('ลบรูปภาพสำเร็จ');

  // Image management locators
  readonly idCardImageTitleLocator: Locator = this.page.getByRole('heading', {
    name: 'ภาพถ่ายบัตรประชาชน',
  });
  readonly faceImageTitleLocator: Locator = this.page.getByRole('heading', { name: 'ภาพถ่ายใบหน้า' });
  readonly deleteOptionLocator: Locator = this.page.getByText('ลบ');
  readonly uploadOptionLocator: Locator = this.page.getByText('อัปโหลด');
  readonly confirmDeleteButtonLocator: Locator = this.page.getByRole('button', { name: 'ยืนยัน' });
  readonly emailInputLocator: Locator = this.page.locator('input[type="email"]');
  readonly confirmOkButtonLocator: Locator = this.page.getByRole('button', { name: 'ตกลง' });
  readonly defaultImageLocator: Locator = this.page.locator('img[src="/static/assets/image-card-default.svg"]');
  readonly fileInputLocator: Locator = this.page.locator('input[type="file"]');

  // Image crop modal locators
  readonly imageCropModalLocator: Locator = this.page.locator('css=div.antd-img-crop-modal div.ant-modal-content');
  readonly confirmImageUploadLocator: Locator = this.page.locator(
    'css=div.antd-img-crop-modal div.footer button.ant-btn-primary',
  );

  constructor(page: Page) {
    super(page, '/admin/additionalDocuments');
  }

  // Component admin
  async clickWaitingToEnrollTab(): Promise<this> {
    await this.waitingToEnrollTabLocator.click();
    await this.loadingSkeleton.waitFor({ state: 'hidden' });
    await this.loadingAnimation.waitFor({ state: 'hidden' });

    return this;
  }

  async clickEnrollmentLearningTab(): Promise<this> {
    await this.enrollmentLearningTabLocator.click();
    await this.loadingSkeleton.waitFor({ state: 'hidden' });
    await this.loadingAnimation.waitFor({ state: 'hidden' });

    return this;
  }

  async searchUsers(name: string): Promise<this> {
    await this.nameInputLocator.fill(name);
    await this.searchButtonLocator.click();
    await this.loadingSkeleton.waitFor({ state: 'hidden' });
    await this.loadingAnimation.waitFor({ state: 'hidden' });

    return this;
  }

  async viewEnrollmentDetailTLI(): Promise<this> {
    await this.viewDetailButtonLocator.click();

    return this;
  }

  async viewEnrollmentDetail(): Promise<this> {
    await this.viewDetailButtonLocator.click();
    await Promise.all([
      await this.page.waitForLoadState(),
      await expect(this.verifyHeadingDocumentLocator).toBeVisible(),
      await expect(this.verifyHeadingCreditHistoryLocator).toBeVisible(),
    ]);

    return this;
  }

  async requestDocument(text: string, date: Date): Promise<this> {
    await this.loadingSkeleton.waitFor({ state: 'hidden' });
    await this.requestDocumentButtonLocator.click();
    await this.checkBoxCitizenCardLocator.click();
    await this.reasonRequestDocumentInputLocator.fill(text);

    await this.tabSelectDateLocator.click();

    const expectedMonth = CalendarHelper.getFullBuddhistNoSetZeroDateString(date).split(' ')[1];
    const currentMonth = await this.calendarHeaderLocator.textContent();

    if (currentMonth?.trim() !== expectedMonth) {
      await this.nextMonthButtonLocator.click();
    }

    await this.page
      .locator(
        this.selectDateOnCalendarLocator.replace('${date}', CalendarHelper.getBuddhistDateString(new Date(date))),
      )
      .click();

    await expect(this.inputExpiredAtLocator).not.toHaveValue('เลือก วัน/เดือน/ปี ที่ส่งเอกสาร');
    await expect(this.inputExpiredAtLocator).not.toHaveValue('');

    await this.submitRequestDocumentButtonLocator.click();
    await expect(this.toastCreateAdditionDocumentSuccessLocator).toBeVisible();

    return this;
  }

  async searchNameAndVerifyUserEnrollmentCourse(
    firstname: string,
    lastname: string,
    courseName: string,
  ): Promise<this> {
    await this.searchFullnNameLocator.fill(`${firstname} ${lastname}`);
    await this.page.keyboard.press('Enter');
    await this.loadingAnimation.waitFor({ state: 'hidden' });
    await this.searchResultLocator.waitFor();
    await expect(this.columnFullNameLocator).toContainText(`${firstname}${lastname}`);
    await expect(this.columnCourseNameLocator).toContainText(courseName);

    return this;
  }
  async verifyEnrollmentStatus(status: string): Promise<this> {
    await expect(this.enrollmentStatusOnFooterLocator).toBeVisible();
    switch (status) {
      case 'in_progress': {
        await expect(this.enrollmentStatusOnFooterLocator).toContainText('กำลังเรียน');
        break;
      }
      default: {
        /* empty */
      }
    }

    return this;
  }

  async editRemark(remark: string): Promise<this> {
    await this.editRemarkButtonLocator.click();
    await this.remarkInputLocator.fill(remark);
    await this.submitRemarkButtonLocator.click();
    await expect(this.toastSuccessLocator).toBeVisible();

    return this;
  }

  async verifyRemarkText(expectedRemark: string): Promise<this> {
    await expect(this.remarkDisplayTextLocator).toBeVisible();
    await expect(this.remarkDisplayTextLocator).toHaveText(expectedRemark);

    return this;
  }

  // Image management methods
  async deleteIdCardImage(): Promise<this> {
    // Find the ID card title, then navigate to the next div which contains the image section
    console.log();
    const idCardTitle = this.idCardImageTitleLocator;
    await expect(this.idCardImageTitleLocator).toBeVisible();

    // The image section is in the next ant-space-item div after the title
    const idCardSection = idCardTitle.locator('..').locator('+ div.ant-space-item');
    await expect(idCardSection).toBeVisible();

    // Find the dropdown button within this section
    const dropdownButton = idCardSection.locator('button.ant-dropdown-trigger.buttonMoreList');
    await expect(dropdownButton).toBeVisible();
    await dropdownButton.click();

    // Click delete option
    await this.deleteOptionLocator.click();

    // Input email in the confirmation modal
    await expect(this.emailInputLocator).toBeVisible();
    await this.emailInputLocator.fill('<EMAIL>');

    // Click OK to confirm deletion
    await this.confirmOkButtonLocator.click();

    // Verify toast message
    await expect(this.toastDeleteImageSuccessLocator).toBeVisible();
    await this.toastDeleteImageSuccessLocator.waitFor({ state: 'hidden' });

    // Verify image src is now default
    const imageInSection = idCardSection.locator('img').first();
    await expect(imageInSection).toHaveAttribute('src', '/static/assets/image-card-default.svg');

    return this;
  }

  async uploadIdCardImage(imagePath: string): Promise<this> {
    // Find the ID card title, then navigate to the next div which contains the image section
    const idCardTitle = this.idCardImageTitleLocator;
    await expect(idCardTitle).toBeVisible();

    // The image section is in the next ant-space-item div after the title
    const idCardSection = idCardTitle.locator('..').locator('+ div.ant-space-item');
    await expect(idCardSection).toBeVisible();

    // Find the dropdown button within this section
    const dropdownButton = idCardSection.locator('button.ant-dropdown-trigger.buttonMoreList');
    await expect(dropdownButton).toBeVisible();
    await dropdownButton.click();

    // Click upload option
    await this.uploadOptionLocator.click();

    // Handle file upload - look for file input within the section
    const [fileChooser] = await Promise.all([
      this.page.waitForEvent('filechooser'),
      // Click the file input within this section
      idCardSection.locator('input[type="file"]').click(),
    ]);
    const filePath = `${path.join(__dirname, '../../../../', imagePath)}`;
    await fileChooser.setFiles(filePath);

    // Handle image crop modal if it appears
    try {
      await this.imageCropModalLocator.waitFor({ timeout: 5000 });
      await (await this.imageCropModalLocator.elementHandle()).waitForElementState('stable');
      await this.confirmImageUploadLocator.click();
      await this.imageCropModalLocator.waitFor({ state: 'hidden' });
    } catch (error) {
      // Image crop modal might not appear for all image types
      console.log('Image crop modal did not appear, continuing...');
    }

    // Wait a moment for the image to load
    await this.page.waitForTimeout(2000);

    // Verify new image appears (src should not be default)
    const imageInSection = idCardSection.locator('img').first();
    await expect(imageInSection).not.toHaveAttribute('src', '/static/assets/image-card-default.svg');

    return this;
  }

  async deleteFaceImage(): Promise<this> {
    // Find the face image title, then navigate to the next div which contains the image section
    const faceTitle = this.faceImageTitleLocator;
    await expect(faceTitle).toBeVisible();

    // The image section is in the next ant-space-item div after the title
    const faceSection = faceTitle.locator('..').locator('+ div.ant-space-item');
    await expect(faceSection).toBeVisible();

    // Find the dropdown button within this section
    const dropdownButton = faceSection.locator('button.ant-dropdown-trigger.buttonMoreList');
    await expect(dropdownButton).toBeVisible();
    await dropdownButton.click();

    // Click delete option
    await this.deleteOptionLocator.click();

    // Confirm deletion
    await this.confirmDeleteButtonLocator.click();

    // Verify toast message
    await expect(this.toastDeleteImageSuccessLocator).toBeVisible();
    await this.toastDeleteImageSuccessLocator.waitFor({ state: 'hidden' });

    // Verify image src is now default
    const imageInSection = faceSection.locator('img').first();
    await expect(imageInSection).toHaveAttribute('src', '/static/assets/image-card-default.svg');

    return this;
  }

  async uploadFaceImage(imagePath: string): Promise<this> {
    // Find the face image title, then navigate to the next div which contains the image section
    const faceTitle = this.faceImageTitleLocator;
    await expect(faceTitle).toBeVisible();

    // The image section is in the next ant-space-item div after the title
    const faceSection = faceTitle.locator('..').locator('+ div.ant-space-item');
    await expect(faceSection).toBeVisible();

    // Find the dropdown button within this section
    const dropdownButton = faceSection.locator('button.ant-dropdown-trigger.buttonMoreList');
    await expect(dropdownButton).toBeVisible();
    await dropdownButton.click();

    // Click upload option
    await this.uploadOptionLocator.click();

    // Handle file upload - look for file input within the section
    const [fileChooser] = await Promise.all([
      this.page.waitForEvent('filechooser'),
      // Click the file input within this section
      faceSection.locator('input[type="file"]').click(),
    ]);
    const filePath = `${path.join(__dirname, '../../../../', imagePath)}`;
    await fileChooser.setFiles(filePath);

    // Handle image crop modal if it appears
    try {
      await this.imageCropModalLocator.waitFor({ timeout: 5000 });
      await (await this.imageCropModalLocator.elementHandle()).waitForElementState('stable');
      await this.confirmImageUploadLocator.click();
      await this.imageCropModalLocator.waitFor({ state: 'hidden' });
    } catch (error) {
      // Image crop modal might not appear for all image types
      console.log('Image crop modal did not appear, continuing...');
    }

    // Wait a moment for the image to load
    await this.page.waitForTimeout(2000);

    // Verify new image appears (src should not be default)
    const imageInSection = faceSection.locator('img').first();
    await expect(imageInSection).not.toHaveAttribute('src', '/static/assets/image-card-default.svg');

    return this;
  }
}
